use diesel::prelude::*;
use serde::{Deserialize, Serialize};

use crate::models::user::User;
use crate::models::household::Household;
use crate::schema::user_households;

#[derive(Debug, Clone, Queryable, Identifiable, Associations, Serialize, Deserialize)]
#[diesel(table_name = user_households)]
#[diesel(primary_key(user_id, household_id))]
#[diesel(belongs_to(User))]
#[diesel(belongs_to(Household))]
pub struct UserHousehold {
    pub user_id: i32,
    pub household_id: i32,
    pub role: String,
}

#[derive(Insertable)]
#[diesel(table_name = user_households)]
pub struct NewUserHousehold {
    pub user_id: i32,
    pub household_id: i32,
    pub role: &'static str,
}