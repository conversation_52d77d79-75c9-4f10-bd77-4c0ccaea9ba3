use diesel::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::NaiveDateTime;

#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = crate::schema::wishlists)]
#[diesel(check_for_backend(diesel::sqlite::Sqlite))]
pub struct Wishlist {
    pub id: i32,
    pub user_id: i32,
    pub item_type: String, // 'plant' or 'seed'
    pub item_id: i32,
    pub notes: Option<String>,
    pub priority: Option<i32>, // 1=low, 2=medium, 3=high
    pub created_at: Option<NaiveDateTime>,
    pub updated_at: Option<NaiveDateTime>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = crate::schema::wishlists)]
pub struct NewWishlist<'a> {
    pub user_id: i32,
    pub item_type: &'a str,
    pub item_id: i32,
    pub notes: Option<&'a str>,
    pub priority: Option<i32>,
}

impl Wishlist {
    pub fn find_by_user_and_type(
        conn: &mut SqliteConnection,
        user_id_param: i32,
        item_type_param: &str,
    ) -> QueryResult<Vec<Wishlist>> {
        use crate::schema::wishlists::dsl::*;
        wishlists
            .filter(user_id.eq(user_id_param))
            .filter(item_type.eq(item_type_param))
            .load::<Wishlist>(conn)
    }

    pub fn find_by_user(
        conn: &mut SqliteConnection,
        user_id_param: i32,
    ) -> QueryResult<Vec<Wishlist>> {
        use crate::schema::wishlists::dsl::*;
        wishlists
            .filter(user_id.eq(user_id_param))
            .load::<Wishlist>(conn)
    }

    pub fn exists(
        conn: &mut SqliteConnection,
        user_id_param: i32,
        item_type_param: &str,
        item_id_param: i32,
    ) -> QueryResult<bool> {
        use crate::schema::wishlists::dsl::*;
        let count: i64 = wishlists
            .filter(user_id.eq(user_id_param))
            .filter(item_type.eq(item_type_param))
            .filter(item_id.eq(item_id_param))
            .count()
            .get_result(conn)?;
        Ok(count > 0)
    }

    pub fn remove(
        conn: &mut SqliteConnection,
        user_id_param: i32,
        item_type_param: &str,
        item_id_param: i32,
    ) -> QueryResult<usize> {
        use crate::schema::wishlists::dsl::*;
        diesel::delete(
            wishlists
                .filter(user_id.eq(user_id_param))
                .filter(item_type.eq(item_type_param))
                .filter(item_id.eq(item_id_param))
        ).execute(conn)
    }
}
