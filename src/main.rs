use actix_files as fs;
use actix_session::{storage::CookieSessionStore, SessionMiddleware};
use actix_web::{cookie::Key, middleware, web, App, HttpServer};
use diesel::r2d2::{self, ConnectionManager};
use diesel::{QueryDsl, RunQuery<PERSON>l, SqliteConnection};
use dotenv::dotenv;
use std::env;
use tokio::time::{Duration};
use r2d2::Pool;
use diesel::expression_methods::ExpressionMethods;
use crate::models::notification::Notification;
use crate::schema::notifications;
use crate::utils::rate_limiter::{RateLimiter, RateLimiterConfig};
use crate::utils::csrf::CsrfProtection;

pub mod models;
pub mod routes;
pub mod schema;
pub mod services;
pub mod utils;

pub type DbPool = r2d2::Pool<ConnectionManager<SqliteConnection>>;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv().ok();
    env_logger::init();

    let database_url = env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let manager = ConnectionManager::<SqliteConnection>::new(database_url);
    let pool = r2d2::Pool::builder()
        .build(manager)
        .expect("Failed to create pool.");

    // Initialize superadmin
    println!("Initializing admin user...");
    {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");
        utils::init::initialize_admin_user(&mut conn);
    }
    println!("Admin user initialized.");

    println!("Setting up server...");

    // Start the notification scheduler
    // let pool_clone = pool.clone();
    // actix_web::rt::spawn(async move {
    //     loop {
    //         if let Err(e) = check_and_send_notifications(pool_clone.clone()).await {
    //             log::error!("Error checking notifications: {}", e);
    //         }
    //         tokio::time::sleep(Duration::from_secs(60)).await;
    //     }
    // });

    log::info!("Starting server at http://127.0.0.1:8080");

    // Generate a secure random key for cookie encryption
    let secret_key = Key::generate();

    // Configure rate limiter
    let rate_limiter_config = RateLimiterConfig {
        max_requests: 5,  // 5 login attempts
        window_seconds: 60, // per minute
    };
    let rate_limiter = RateLimiter::new(rate_limiter_config);

    println!("Creating HTTP server...");

    println!("Starting server on 127.0.0.1:8080");

    let server = HttpServer::new(move || {
        App::new()
            .wrap(middleware::Logger::default())
            .wrap(
                SessionMiddleware::builder(
                    CookieSessionStore::default(),
                    secret_key.clone(),
                )
                .cookie_secure(false) // Allow HTTP in development
                .build()
            )
            .app_data(web::Data::new(pool.clone()))
            .service(fs::Files::new("/static", "./static"))
            .configure(routes::init)
    })
    .bind("127.0.0.1:8080")
    .map_err(|e| {
        println!("Failed to bind server: {}", e);
        e
    })?;

    println!("Server bound successfully, starting...");

    server.run().await
}

async fn check_and_send_notifications(pool: Pool<ConnectionManager<SqliteConnection>>) -> Result<(), Box<dyn std::error::Error>> {
    let mut conn = pool.get()?;
    let now = chrono::Utc::now().naive_utc();

    let pending_notifications = notifications::table
        .filter(notifications::scheduled_time.le(now))
        .filter(notifications::sent.eq(false))
        .load::<Notification>(&mut conn)?;

    for notification in pending_notifications {
        // Send the notification (e.g., print to console, send email, etc.)
        log::info!("Notification for user {}: {}", notification.user_id, notification.message);

        // Mark as sent
        diesel::update(notifications::table.find(notification.id))
            .set(notifications::sent.eq(true))
            .execute(&mut conn)?;
    }

    Ok(())
}
fn list_tables(conn: &mut SqliteConnection) {
    use diesel::sql_query;
    use diesel::deserialize::QueryableByName;
    use diesel::sql_types::Text;

    #[derive(QueryableByName, Debug)]
    struct TableName {
        #[diesel(sql_type = Text)]
        name: String,
    }

    let results = sql_query("SELECT name FROM sqlite_master WHERE type='table'")
        .load::<TableName>(conn)
        .expect("Failed to list tables");

    println!("Tables in the database:");
    for table in results {
        println!("{}", table.name);
    }
}
