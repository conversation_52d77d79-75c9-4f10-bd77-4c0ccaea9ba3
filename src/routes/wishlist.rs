use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;

use crate::models::plant::Plant;
use crate::models::seed::Seed;
use crate::models::wishlist::{Wishlist, NewWishlist};
use crate::schema::{plants, seeds, wishlists};
use crate::utils::templates::render_template_with_context;
use crate::utils::auth::is_authenticated;
use crate::DbPool;

#[derive(Deserialize)]
pub struct WishlistForm {
    pub item_type: String, // "plant" or "seed"
    pub item_id: i32,
    pub notes: Option<String>,
}

pub async fn plant_wishlist(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap_or(0);
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get all plants
    let all_plants = plants::table
        .load::<Plant>(&mut conn)
        .expect("Error loading plants");

    // Get user's plant wishlist
    let user_wishlist = Wishlist::find_by_user_and_type(&mut conn, user_id, "plant")
        .unwrap_or_default();

    // Create a set of wishlist item IDs for quick lookup
    let wishlist_ids: std::collections::HashSet<i32> = user_wishlist
        .iter()
        .map(|w| w.item_id)
        .collect();

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &all_plants);
    ctx.insert("wishlist_type", "plants");
    ctx.insert("wishlist_ids", &wishlist_ids);
    ctx.insert("user_wishlist", &user_wishlist);

    render_template_with_context("wishlist/plants.html", &mut ctx, &session)
}

pub async fn seed_wishlist(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap_or(0);
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get all seeds
    let all_seeds = seeds::table
        .load::<Seed>(&mut conn)
        .expect("Error loading seeds");

    // Get user's seed wishlist
    let user_wishlist = Wishlist::find_by_user_and_type(&mut conn, user_id, "seed")
        .unwrap_or_default();

    // Create a set of wishlist item IDs for quick lookup
    let wishlist_ids: std::collections::HashSet<i32> = user_wishlist
        .iter()
        .map(|w| w.item_id)
        .collect();

    let mut ctx = tera::Context::new();
    ctx.insert("seeds", &all_seeds);
    ctx.insert("wishlist_type", "seeds");
    ctx.insert("wishlist_ids", &wishlist_ids);
    ctx.insert("user_wishlist", &user_wishlist);

    render_template_with_context("wishlist/seeds.html", &mut ctx, &session)
}

pub async fn add_to_wishlist(
    session: Session,
    form: web::Form<WishlistForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap_or(0);
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Check if item already exists in wishlist
    let exists = Wishlist::exists(&mut conn, user_id, &form.item_type, form.item_id)
        .unwrap_or(false);

    if !exists {
        let new_wishlist_item = NewWishlist {
            user_id,
            item_type: &form.item_type,
            item_id: form.item_id,
            notes: form.notes.as_deref(),
            priority: Some(1), // Default to low priority
        };

        diesel::insert_into(wishlists::table)
            .values(&new_wishlist_item)
            .execute(&mut conn)
            .expect("Error adding item to wishlist");
    }

    let redirect_url = match form.item_type.as_str() {
        "plant" => "/wishlist/plants",
        "seed" => "/wishlist/seeds",
        _ => "/wishlist/plants",
    };

    Ok(HttpResponse::Found()
        .append_header(("Location", redirect_url))
        .finish())
}

pub async fn remove_from_wishlist(
    session: Session,
    form: web::Form<WishlistForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap_or(0);
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    Wishlist::remove(&mut conn, user_id, &form.item_type, form.item_id)
        .expect("Error removing item from wishlist");

    let redirect_url = match form.item_type.as_str() {
        "plant" => "/wishlist/plants",
        "seed" => "/wishlist/seeds",
        _ => "/wishlist/plants",
    };

    Ok(HttpResponse::Found()
        .append_header(("Location", redirect_url))
        .finish())
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/wishlist")
            .route("/plants", web::get().to(plant_wishlist))
            .route("/seeds", web::get().to(seed_wishlist))
            .route("/add", web::post().to(add_to_wishlist))
            .route("/remove", web::post().to(remove_from_wishlist)),
    );
}
