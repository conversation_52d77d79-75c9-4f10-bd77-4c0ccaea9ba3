use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;

use crate::models::plant::Plant;
use crate::models::seed::Seed;
use crate::schema::{plants, seeds};
use crate::utils::templates::render_template_with_context;
use crate::utils::auth::is_authenticated;
use crate::DbPool;

#[derive(Deserialize)]
pub struct WishlistForm {
    pub item_type: String, // "plant" or "seed"
    pub item_id: i32,
    pub notes: Option<String>,
}

pub async fn plant_wishlist(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let all_plants = plants::table
        .load::<Plant>(&mut conn)
        .expect("Error loading plants");

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &all_plants);
    ctx.insert("wishlist_type", "plants");

    render_template_with_context("wishlist/plants.html", &mut ctx, &session)
}

pub async fn seed_wishlist(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let all_seeds = seeds::table
        .load::<Seed>(&mut conn)
        .expect("Error loading seeds");

    let mut ctx = tera::Context::new();
    ctx.insert("seeds", &all_seeds);
    ctx.insert("wishlist_type", "seeds");

    render_template_with_context("wishlist/seeds.html", &mut ctx, &session)
}

pub async fn add_to_wishlist(
    session: Session,
    form: web::Form<WishlistForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap_or(0);
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // For now, we'll just redirect back with a success message
    // In a full implementation, you'd save to a wishlist table
    
    let redirect_url = match form.item_type.as_str() {
        "plant" => "/wishlist/plants",
        "seed" => "/wishlist/seeds",
        _ => "/wishlist/plants",
    };

    Ok(HttpResponse::Found()
        .append_header(("Location", redirect_url))
        .finish())
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/wishlist")
            .route("/plants", web::get().to(plant_wishlist))
            .route("/seeds", web::get().to(seed_wishlist))
            .route("/add", web::post().to(add_to_wishlist)),
    );
}
