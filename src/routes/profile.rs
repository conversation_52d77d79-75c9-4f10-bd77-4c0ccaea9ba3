use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;

use crate::models::user::User;
use crate::schema::users;
use crate::utils::templates::render_template_with_context;
use crate::utils::auth::is_authenticated;
use crate::DbPool;

#[derive(Deserialize)]
pub struct ProfileForm {
    pub username: String,
    pub email: Option<String>,
    pub full_name: Option<String>,
}

#[derive(Deserialize)]
pub struct PasswordForm {
    pub current_password: String,
    pub new_password: String,
    pub confirm_password: String,
}

// Show user profile
pub async fn profile(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let user = users::table
        .find(user_id)
        .first::<User>(&mut conn)
        .expect("Error loading user");

    let mut ctx = tera::Context::new();
    ctx.insert("user", &user);

    render_template_with_context("profile/profile.html", &mut ctx, &session)
}

// Show settings page
pub async fn settings(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let user = users::table
        .find(user_id)
        .first::<User>(&mut conn)
        .expect("Error loading user");

    let mut ctx = tera::Context::new();
    ctx.insert("user", &user);

    render_template_with_context("profile/settings.html", &mut ctx, &session)
}

// Update profile
pub async fn update_profile(
    session: Session,
    form: web::Form<ProfileForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Update user profile
    diesel::update(users::table.find(user_id))
        .set(users::username.eq(&form.username))
        .execute(&mut conn)
        .expect("Error updating user profile");

    // Update session username
    session.insert("username", &form.username)?;

    Ok(HttpResponse::Found()
        .append_header(("Location", "/profile"))
        .finish())
}

// Change password
pub async fn change_password(
    session: Session,
    form: web::Form<PasswordForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    if form.new_password != form.confirm_password {
        let mut ctx = tera::Context::new();
        ctx.insert("error", "New passwords do not match");
        return render_template_with_context("profile/settings.html", &mut ctx, &session);
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let user = users::table
        .find(user_id)
        .first::<User>(&mut conn)
        .expect("Error loading user");

    // Verify current password
    if !crate::utils::hashing::verify_password(&form.current_password, &user.password_hash) {
        let mut ctx = tera::Context::new();
        ctx.insert("error", "Current password is incorrect");
        ctx.insert("user", &user);
        return render_template_with_context("profile/settings.html", &mut ctx, &session);
    }

    // Hash new password
    let new_password_hash = crate::utils::hashing::hash_password(&form.new_password)
        .map_err(|_| actix_web::error::ErrorInternalServerError("Password hashing failed"))?;

    // Update password
    diesel::update(users::table.find(user_id))
        .set(users::password_hash.eq(&new_password_hash))
        .execute(&mut conn)
        .expect("Error updating password");

    let mut ctx = tera::Context::new();
    ctx.insert("success", "Password updated successfully");
    ctx.insert("user", &user);

    render_template_with_context("profile/settings.html", &mut ctx, &session)
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("")
            .route("/profile", web::get().to(profile))
            .route("/settings", web::get().to(settings))
            .route("/profile/update", web::post().to(update_profile))
            .route("/profile/change-password", web::post().to(change_password)),
    );
}
