use crate::models::user::{NewUser, User};
use crate::utils::hashing::hash_password;
use diesel::prelude::*;
use diesel::SelectableHelper;

pub fn initialize_admin_user(conn: &mut SqliteConnection) {
    use crate::schema::users;

    // Check if any users exist at all
    let user_count: i64 = users::table.count().get_result(conn).unwrap_or(0);

    // Only create default admin if no users exist AND it's explicitly requested
    // This allows the first registered user to become superadmin instead
    if user_count == 0 && std::env::var("CREATE_DEFAULT_ADMIN").is_ok() {
        let admin_exists = users::table
            .filter(users::role.eq("superadmin"))
            .select(User::as_select())
            .first::<User>(conn)
            .optional()
            .expect("Error checking for existing superadmin");

        if admin_exists.is_none() {
            let hashed_password = match hash_password("admin") {
                Ok(hash) => hash,
                Err(e) => {
                    eprintln!("Error hashing admin password: {}", e);
                    return;
                }
            };
            let new_user = NewUser {
                username: "admin",
                password_hash: &hashed_password,
                role: "superadmin",
            };

            diesel::insert_into(users::table)
                .values(&new_user)
                .execute(conn)
                .expect("Error creating superadmin user");

            println!("Created default admin user (username: admin, password: admin)");
        }
    }
}
