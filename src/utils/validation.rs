//! Input validation utilities
//!
//! This module provides functions for validating user input to prevent
//! security issues and ensure data integrity.

use regex::Regex;
use lazy_static::lazy_static;
use crate::utils::error::{AppError, AppResult};

lazy_static! {
    /// Regular expression for validating usernames
    /// Allows alphanumeric characters, underscores, and hyphens, 3-30 characters
    static ref USERNAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_-]{3,30}$").unwrap();

    /// Regular expression for validating email addresses
    static ref EMAIL_REGEX: Regex = Regex::new(
        r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"
    ).unwrap();

    /// Regular expression for validating URLs
    static ref URL_REGEX: Regex = Regex::new(
        r"^(https?|ftp)://[^\s/$.?#].[^\s]*$"
    ).unwrap();
}

/// Validates a username
///
/// # Arguments
/// * `username` - The username to validate
///
/// # Returns
/// * `AppResult<()>` - Ok if valid, ValidationError if invalid
pub fn validate_username(username: &str) -> AppResult<()> {
    if !USERNAME_REGEX.is_match(username) {
        return Err(AppError::ValidationError(
            "Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens".to_string()
        ));
    }
    Ok(())
}

/// Validates an email address
///
/// # Arguments
/// * `email` - The email address to validate
///
/// # Returns
/// * `AppResult<()>` - Ok if valid, ValidationError if invalid
pub fn validate_email(email: &str) -> AppResult<()> {
    if !EMAIL_REGEX.is_match(email) {
        return Err(AppError::ValidationError(
            "Invalid email address format".to_string()
        ));
    }
    Ok(())
}

/// Validates a URL
///
/// # Arguments
/// * `url` - The URL to validate
///
/// # Returns
/// * `AppResult<()>` - Ok if valid, ValidationError if invalid
pub fn validate_url(url: &str) -> AppResult<()> {
    if !URL_REGEX.is_match(url) {
        return Err(AppError::ValidationError(
            "Invalid URL format".to_string()
        ));
    }
    Ok(())
}

/// Validates a password
///
/// # Arguments
/// * `password` - The password to validate
///
/// # Returns
/// * `AppResult<()>` - Ok if valid, ValidationError if invalid
pub fn validate_password(password: &str) -> AppResult<()> {
    if password.len() < 6 {
        return Err(AppError::ValidationError(
            "Password must be at least 6 characters long".to_string()
        ));
    }

    if password.len() > 128 {
        return Err(AppError::ValidationError(
            "Password must be no more than 128 characters long".to_string()
        ));
    }

    // For development/testing, we'll use simpler validation
    // In production, you might want to add more complex requirements
    Ok(())
}

/// Sanitizes a string to prevent XSS attacks
///
/// # Arguments
/// * `input` - The string to sanitize
///
/// # Returns
/// * `String` - The sanitized string
pub fn sanitize_html(input: &str) -> String {
    let mut output = input.replace('<', "&lt;");
    output = output.replace('>', "&gt;");
    output = output.replace('"', "&quot;");
    output = output.replace('\'', "&#39;");
    output = output.replace('&', "&amp;");
    output
}

/// Validates and sanitizes a text input
///
/// # Arguments
/// * `input` - The text to validate and sanitize
/// * `min_length` - The minimum allowed length
/// * `max_length` - The maximum allowed length
///
/// # Returns
/// * `AppResult<String>` - The sanitized string if valid, ValidationError if invalid
pub fn validate_and_sanitize_text(input: &str, min_length: usize, max_length: usize) -> AppResult<String> {
    if input.len() < min_length {
        return Err(AppError::ValidationError(
            format!("Text must be at least {} characters long", min_length)
        ));
    }

    if input.len() > max_length {
        return Err(AppError::ValidationError(
            format!("Text must be no more than {} characters long", max_length)
        ));
    }

    Ok(sanitize_html(input))
}
