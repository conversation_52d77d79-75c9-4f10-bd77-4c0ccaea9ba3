#!/usr/bin/env python3
import requests
import re

session = requests.Session()

print("Testing login flow...")

# Get login form
response = session.get('http://127.0.0.1:8080/auth/login')
print(f"Login form: {response.status_code}")

# Extract CSRF token
match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
csrf_token = match.group(1) if match else None
print(f"CSRF token: {csrf_token}")

# Try login with admin user
data = {
    'username': 'admin',
    'password': 'admin123',
    'csrf_token': csrf_token
}

response = session.post('http://127.0.0.1:8080/auth/login', data=data)
print(f"Login response: {response.status_code} -> {response.headers.get('Location', '')}")

# Test homepage after login
response = session.get('http://127.0.0.1:8080/')
print(f"Homepage after login: {response.status_code}")
if response.status_code != 200:
    print(f"Error content: {response.text[:500]}")
else:
    print("Homepage loaded successfully after login!")
    print(f"Content length: {len(response.text)}")
