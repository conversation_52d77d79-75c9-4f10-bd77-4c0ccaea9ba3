warning: unused import: `crate::schema::season_plans`
  --> src/models/season_plan_plant.rs:98:13
   |
98 |         use crate::schema::season_plans;
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unused import: `crate::schema::properties`
  --> src/models/season_plan_plant.rs:99:13
   |
99 |         use crate::schema::properties;
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::schema::user_households`
   --> src/models/season_plan_plant.rs:100:13
    |
100 |         use crate::schema::user_households;
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `render_template`
 --> src/routes/admin.rs:4:31
  |
4 | use crate::utils::templates::{render_template, render_template_with_context};
  |                               ^^^^^^^^^^^^^^^

warning: unused import: `render_template`
  --> src/routes/mod.rs:15:31
   |
15 | use crate::utils::templates::{render_template, render_template_with_context};
   |                               ^^^^^^^^^^^^^^^

warning: unused import: `serde_json::json`
 --> src/services/herba_gatherer.rs:3:5
  |
3 | use serde_json::json;
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/services/herba_gatherer.rs:4:5
  |
4 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `regex::Regex`
 --> src/services/herba_gatherer.rs:8:5
  |
8 | use regex::Regex;
  |     ^^^^^^^^^^^^

warning: unused imports: `SeasonPlanPlant`, `SeasonPlan`, and `Season`
 --> src/services/season_planner.rs:1:40
  |
1 | use crate::models::{Plant, HerbaPlant, SeasonPlan, SeasonPlanPlant, GrowingArea, Property, Season};
  |                                        ^^^^^^^^^^  ^^^^^^^^^^^^^^^                         ^^^^^^

warning: unused import: `Datelike`
 --> src/services/season_planner.rs:4:25
  |
4 | use chrono::{NaiveDate, Datelike};
  |                         ^^^^^^^^

warning: unused import: `serde_json::json`
 --> src/services/season_planner.rs:5:5
  |
5 | use serde_json::json;
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/services/season_planner.rs:6:5
  |
6 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Datelike`
 --> src/services/notification_service.rs:4:25
  |
4 | use chrono::{NaiveDate, Datelike, Duration};
  |                         ^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/services/notification_service.rs:5:5
  |
5 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused variable: `user_id`
  --> src/models/season_plan_plant.rs:96:59
   |
96 |     pub fn find_all_for_user(conn: &mut SqliteConnection, user_id: i32) -> QueryResult<Vec<SeasonPlanPlant>> {
   |                                                           ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `infobox`
   --> src/services/herba_gatherer.rs:281:37
    |
281 |     fn extract_infobox_value(&self, infobox: &scraper::ElementRef, label: &str) -> Option<String> {
    |                                     ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_infobox`

warning: unused variable: `label`
   --> src/services/herba_gatherer.rs:281:68
    |
281 |     fn extract_infobox_value(&self, infobox: &scraper::ElementRef, label: &str) -> Option<String> {
    |                                                                    ^^^^^ help: if this is intentional, prefix it with an underscore: `_label`

warning: unused variable: `document`
   --> src/services/herba_gatherer.rs:288:40
    |
288 |     fn extract_cultivation_info(&self, document: &Html, keywords: &[&str]) -> Option<String> {
    |                                        ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_document`

warning: unused variable: `keywords`
   --> src/services/herba_gatherer.rs:288:57
    |
288 |     fn extract_cultivation_info(&self, document: &Html, keywords: &[&str]) -> Option<String> {
    |                                                         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_keywords`

warning: unused variable: `document`
   --> src/services/herba_gatherer.rs:295:37
    |
295 |     fn extract_text_by_label(&self, document: &Html, label: &str) -> Option<String> {
    |                                     ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_document`

warning: unused variable: `label`
   --> src/services/herba_gatherer.rs:295:54
    |
295 |     fn extract_text_by_label(&self, document: &Html, label: &str) -> Option<String> {
    |                                                      ^^^^^ help: if this is intentional, prefix it with an underscore: `_label`

warning: unused variable: `conn`
   --> src/services/season_planner.rs:194:9
    |
194 |         conn: &mut SqliteConnection,
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_conn`

warning: unused variable: `conn`
   --> src/services/season_planner.rs:269:9
    |
269 |         conn: &mut SqliteConnection,
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_conn`

warning: variable does not need to be mutable
   --> src/services/season_planner.rs:270:9
    |
270 |         mut recommendations: Vec<PlantingRecommendation>,
    |         ----^^^^^^^^^^^^^^^
    |         |
    |         help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: unused variable: `h`
   --> src/services/season_planner.rs:301:21
    |
301 |         if let Some(h) = herba {
    |                     ^ help: if this is intentional, prefix it with an underscore: `_h`

warning: unused variable: `grid`
   --> src/services/season_planner.rs:312:9
    |
312 |         grid: &PlacementGrid,
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_grid`

warning: unused variable: `space_needed`
   --> src/services/season_planner.rs:313:9
    |
313 |         space_needed: f32,
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_space_needed`

warning: unused variable: `plant`
   --> src/services/season_planner.rs:314:9
    |
314 |         plant: &Plant,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_plant`

warning: unused variable: `herba`
   --> src/services/season_planner.rs:315:9
    |
315 |         herba: &Option<HerbaPlant>,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_herba`

warning: unused variable: `existing_recommendations`
   --> src/services/season_planner.rs:316:9
    |
316 |         existing_recommendations: &[PlantingRecommendation],
    |         ^^^^^^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_existing_recommendations`

warning: unused variable: `end_date`
   --> src/services/season_planner.rs:325:9
    |
325 |         end_date: &NaiveDate,
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_end_date`

warning: unused variable: `herba`
   --> src/services/season_planner.rs:342:9
    |
342 |         herba: &Option<HerbaPlant>,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_herba`

warning: unused variable: `existing_recommendations`
   --> src/services/season_planner.rs:354:9
    |
354 |         existing_recommendations: &[PlantingRecommendation],
    |         ^^^^^^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_existing_recommendations`

warning: unused variable: `herba`
   --> src/services/season_planner.rs:355:9
    |
355 |         herba: &Option<HerbaPlant>,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_herba`

warning: unused variable: `plant`
   --> src/services/season_planner.rs:363:9
    |
363 |         plant: &Plant,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_plant`

warning: unused variable: `notification_type`
   --> src/services/notification_service.rs:193:17
    |
193 |             let notification_type = match request.notification_type {
    |                 ^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_notification_type`

warning: unused variable: `priority`
   --> src/services/notification_service.rs:202:17
    |
202 |             let priority = match request.priority {
    |                 ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_priority`

warning: unused variable: `conn`
   --> src/services/notification_service.rs:279:9
    |
279 |         conn: &mut SqliteConnection,
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_conn`

warning: unused variable: `plant`
   --> src/services/notification_service.rs:373:9
    |
373 |         plant: &Plant,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_plant`

warning: fields `total_area`, `is_indoor`, `soil_quality`, `sun_exposure`, and `water_access` are never read
   --> src/services/season_planner.rs:400:5
    |
399 | struct GrowingSpaceAnalysis {
    |        -------------------- fields in this struct
400 |     total_area: f32,
    |     ^^^^^^^^^^
401 |     available_area: f32,
402 |     is_indoor: bool,
    |     ^^^^^^^^^
403 |     soil_quality: SoilQuality,
    |     ^^^^^^^^^^^^
404 |     sun_exposure: SunExposure,
    |     ^^^^^^^^^^^^
405 |     water_access: WaterAccess,
    |     ^^^^^^^^^^^^
    |
    = note: `GrowingSpaceAnalysis` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis
    = note: `#[warn(dead_code)]` on by default

warning: variants `Poor`, `Good`, and `Excellent` are never constructed
   --> src/services/season_planner.rs:416:5
    |
415 | enum SoilQuality {
    |      ----------- variants in this enum
416 |     Poor,
    |     ^^^^
417 |     Medium,
418 |     Good,
    |     ^^^^
419 |     Excellent,
    |     ^^^^^^^^^
    |
    = note: `SoilQuality` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: variants `FullShade`, `PartialShade`, and `PartialSun` are never constructed
   --> src/services/season_planner.rs:424:5
    |
423 | enum SunExposure {
    |      ----------- variants in this enum
424 |     FullShade,
    |     ^^^^^^^^^
425 |     PartialShade,
    |     ^^^^^^^^^^^^
426 |     PartialSun,
    |     ^^^^^^^^^^
    |
    = note: `SunExposure` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: variants `Poor` and `Excellent` are never constructed
   --> src/services/season_planner.rs:432:5
    |
431 | enum WaterAccess {
    |      ----------- variants in this enum
432 |     Poor,
    |     ^^^^
433 |     Good,
434 |     Excellent,
    |     ^^^^^^^^^
    |
    = note: `WaterAccess` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: `garden_planner_web` (lib) generated 43 warnings (run `cargo fix --lib -p garden_planner_web` to apply 15 suggestions)
warning: unused import: `actix_files as fs`
 --> src/main.rs:1:5
  |
1 | use actix_files as fs;
  |     ^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `SessionMiddleware` and `storage::CookieSessionStore`
 --> src/main.rs:2:21
  |
2 | use actix_session::{storage::CookieSessionStore, SessionMiddleware};
  |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^

warning: unused import: `middleware`
 --> src/main.rs:3:30
  |
3 | use actix_web::{cookie::Key, middleware, web, App, HttpServer};
  |                              ^^^^^^^^^^

warning: unused import: `Duration`
 --> src/main.rs:8:19
  |
8 | use tokio::time::{Duration};
  |                   ^^^^^^^^

warning: unused import: `crate::utils::csrf::CsrfProtection`
  --> src/main.rs:14:5
   |
14 | use crate::utils::csrf::CsrfProtection;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::schema::season_plans`
  --> src/models/season_plan_plant.rs:98:13
   |
98 |         use crate::schema::season_plans;
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `SeasonPlanPlant`, `SeasonPlan`, and `Season`
 --> src/services/season_planner.rs:1:40
  |
1 | ...nt, HerbaPlant, SeasonPlan, SeasonPlanPlant, GrowingArea, Property, Season};
  |                    ^^^^^^^^^^  ^^^^^^^^^^^^^^^                         ^^^^^^

warning: unused variable: `user_id`
  --> src/models/season_plan_plant.rs:96:59
   |
96 | ...tion, user_id: i32) -> QueryResult<Vec<SeasonPlanPlant>> {
   |          ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `infobox`
   --> src/services/herba_gatherer.rs:281:37
    |
281 | ...elf, infobox: &scraper::ElementRef, label: &str) -> Option<String> {
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_infobox`

warning: unused variable: `label`
   --> src/services/herba_gatherer.rs:281:68
    |
281 | ...ntRef, label: &str) -> Option<String> {
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_label`

warning: unused variable: `pool`
  --> src/main.rs:31:9
   |
31 |     let pool = r2d2::Pool::builder()
   |         ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `secret_key`
  --> src/main.rs:59:9
   |
59 |     let secret_key = Key::generate();
   |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_secret_key`

warning: unused variable: `rate_limiter`
  --> src/main.rs:66:9
   |
66 |     let rate_limiter = RateLimiter::new(rate_limiter_config);
   |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_rate_limiter`

warning: function `check_and_send_notifications` is never used
  --> src/main.rs:88:10
   |
88 | async fn check_and_send_notifications(pool: Pool<ConnectionManager<SqliteConnection>>) -> Res...
   |          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(dead_code)]` on by default

warning: function `list_tables` is never used
   --> src/main.rs:109:4
    |
109 | fn list_tables(conn: &mut SqliteConnection) {
    |    ^^^^^^^^^^^

warning: field `name` is never read
   --> src/main.rs:117:9
    |
115 |     struct TableName {
    |            --------- field in this struct
116 |         #[diesel(sql_type = Text)]
117 |         name: String,
    |         ^^^^
    |
    = note: `TableName` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `total_area`, `is_indoor`, `soil_quality`, `sun_exposure`, and `water_access` are never read
   --> src/services/season_planner.rs:400:5
    |
399 | struct GrowingSpaceAnalysis {
    |        -------------------- fields in this struct
400 |     total_area: f32,
    |     ^^^^^^^^^^
401 |     available_area: f32,
402 |     is_indoor: bool,
    |     ^^^^^^^^^
403 |     soil_quality: SoilQuality,
    |     ^^^^^^^^^^^^
404 |     sun_exposure: SunExposure,
    |     ^^^^^^^^^^^^
405 |     water_access: WaterAccess,
    |     ^^^^^^^^^^^^
    |
    = note: `GrowingSpaceAnalysis` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: `garden_planner_web` (bin "garden_planner_web") generated 54 warnings (37 duplicates) (run `cargo fix --bin "garden_planner_web"` to apply 7 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.11s
warning: the following packages contain code that will be rejected by a future version of Rust: nom v4.1.1
note: to see what the problems were, use the option `--future-incompat-report`, or run `cargo report future-incompatibilities --id 1`
     Running `target/debug/garden_planner_web`
Initializing admin user...
Admin user initialized.
Setting up server...
[2025-05-28T08:09:43Z INFO  garden_planner_web] Starting server at http://127.0.0.1:8080
Creating HTTP server...
Starting server on 127.0.0.1:8080
Server bound successfully, starting...
[2025-05-28T08:09:43Z INFO  actix_server::builder] starting 16 workers
[2025-05-28T08:09:43Z INFO  actix_server::server] Actix runtime found; starting in Actix runtime
[2025-05-28T08:09:43Z INFO  actix_server::server] starting service: "actix-web-service-127.0.0.1:8080", workers: 16, listening on: 127.0.0.1:8080
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
Configuring app...
