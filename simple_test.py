#!/usr/bin/env python3
"""
Simple test for Garden Planner Application
"""

import requests
import re
import random

def get_csrf_token(html_content):
    """Extract CSRF token from HTML"""
    match = re.search(r'name="csrf_token" value="([^"]+)"', html_content)
    return match.group(1) if match else None

def test_registration_flow():
    """Test the registration and household creation flow"""
    base_url = "http://127.0.0.1:8080"
    session = requests.Session()
    
    print("Testing registration flow...")
    
    # Test homepage
    response = session.get(f"{base_url}/")
    assert response.status_code == 200
    print("✓ Homepage loads")
    
    # Test registration form
    response = session.get(f"{base_url}/auth/register")
    assert response.status_code == 200
    csrf_token = get_csrf_token(response.text)
    assert csrf_token is not None
    print("✓ Registration form loads with CSRF token")
    
    # Submit registration
    username = f"testuser_{random.randint(1000, 9999)}"
    password = "testpass123"
    
    data = {
        'username': username,
        'password': password,
        'password_confirm': password,
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{base_url}/auth/register", data=data)
    print(f"Registration response status: {response.status_code}")
    
    # Should either redirect (302) or show household page (200)
    if response.status_code == 302:
        print("✓ Registration successful (redirected)")
    elif response.status_code == 200 and "household" in response.text.lower():
        print("✓ Registration successful (on household page)")
    else:
        print(f"❌ Registration failed: {response.status_code}")
        print(response.text[:500])
        return False
    
    # Test household creation
    response = session.get(f"{base_url}/wizard/household")
    assert response.status_code == 200
    csrf_token = get_csrf_token(response.text)
    assert csrf_token is not None
    print("✓ Household creation form loads with CSRF token")
    
    # Submit household creation
    household_name = f"Test Household {random.randint(100, 999)}"
    data = {
        'name': household_name,
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{base_url}/wizard/household", data=data)
    print(f"Household creation response status: {response.status_code}")
    
    if response.status_code == 302:
        print("✓ Household creation successful")
        return True
    else:
        print(f"❌ Household creation failed: {response.status_code}")
        print(response.text[:500])
        return False

def test_theme_toggle():
    """Test that theme toggle is visible"""
    base_url = "http://127.0.0.1:8080"
    
    print("Testing theme toggle visibility...")
    
    # Test on homepage (guest)
    response = requests.get(f"{base_url}/")
    assert response.status_code == 200
    assert 'id="theme-toggle"' in response.text
    assert 'theme-toggle-dark-icon' in response.text
    assert 'theme-toggle-light-icon' in response.text
    print("✓ Theme toggle visible for guests")
    
    return True

if __name__ == "__main__":
    try:
        success1 = test_theme_toggle()
        success2 = test_registration_flow()
        
        if success1 and success2:
            print("\n🎉 All tests passed!")
        else:
            print("\n❌ Some tests failed")
            exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        exit(1)
