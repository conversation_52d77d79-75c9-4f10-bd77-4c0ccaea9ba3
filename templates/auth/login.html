{% extends "base.html" %}
{% block title %}Login{% endblock %}
{% block content %}
<div class="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow mt-10">
  <h2 class="text-2xl font-bold mb-4 text-center">Login to Gardening Planner</h2>
  {% if error %}
  <div class="mb-4 p-4 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 rounded">
    <div class="flex items-center">
      <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      {{ error }}
    </div>
  </div>
  {% endif %}
  <form method="post" action="/auth/login" class="space-y-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    <div>
      <label for="username" class="block text-sm font-medium">Username</label>
      <input type="text" id="username" name="username" required class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
    </div>
    <div>
      <label for="password" class="block text-sm font-medium">Password</label>
      <input type="password" id="password" name="password" required class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
    </div>
    <button type="submit" class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Login</button>
  </form>
  <p class="mt-4 text-center">Don't have an account? <a href="/auth/register" class="text-green-600 hover:underline">Register here</a></p>
</div>
{% endblock %}
