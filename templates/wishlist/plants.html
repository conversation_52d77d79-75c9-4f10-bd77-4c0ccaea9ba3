{% extends "base.html" %}
{% block title %}Plant Wishlist{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold">Plant Wishlist</h1>
            <p class="text-gray-600 dark:text-gray-300 mt-2">Plants you want to grow</p>
        </div>
        <div class="flex space-x-2">
            <a href="/wishlist/seeds" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors">
                Seed Wishlist
            </a>
            <a href="/plants/new" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors">
                Add New Plant
            </a>
        </div>
    </div>

    <!-- Plant Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for plant in plants %}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div class="p-4">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ plant.name }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ plant.common_name | default(value="No common name") }}</p>
                    </div>
                </div>

                <div class="space-y-2 mb-4">
                    {% if plant.scientific_name %}
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        <span class="font-medium">Scientific:</span> {{ plant.scientific_name }}
                    </p>
                    {% endif %}

                    {% if plant.plant_type %}
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        <span class="font-medium">Type:</span> {{ plant.plant_type | title }}
                    </p>
                    {% endif %}

                    {% if plant.description %}
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        {{ plant.description | truncate(length=100) }}
                    </p>
                    {% endif %}
                </div>

                <div class="flex space-x-2">
                    {% if plant.id in wishlist_ids %}
                    <form method="post" action="/wishlist/remove" class="flex-1">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="plant">
                        <input type="hidden" name="item_id" value="{{ plant.id }}">
                        <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm transition-colors">
                            Remove from Wishlist
                        </button>
                    </form>
                    {% else %}
                    <form method="post" action="/wishlist/add" class="flex-1">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="plant">
                        <input type="hidden" name="item_id" value="{{ plant.id }}">
                        <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm transition-colors">
                            Add to Wishlist
                        </button>
                    </form>
                    {% endif %}
                    <a href="/plants/{{ plant.id }}" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm transition-colors">
                        View
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    {% if plants|length == 0 %}
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No plants available</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding some plants to the database.</p>
        <div class="mt-6">
            <a href="/plants/new" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                Add Plant
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
