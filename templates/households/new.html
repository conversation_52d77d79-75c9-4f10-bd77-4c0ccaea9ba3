{% extends "base.html" %}
{% block title %}Create New Household{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="mb-6">
        <h1 class="text-3xl font-bold">Create New Household</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">Set up a new household to organize your garden planning activities.</p>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form action="/households/create" method="post">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Household Name *
                </label>
                <input type="text" id="name" name="name" required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                       placeholder="e.g., Smith Family Garden">
            </div>

            <div class="flex justify-between">
                <a href="/households" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded">
                    Cancel
                </a>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                    Create Household
                </button>
            </div>
        </form>
    </div>

    <div class="mt-6 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                    About Households
                </h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                    <p>Households allow you to:</p>
                    <ul class="list-disc list-inside mt-1">
                        <li>Share properties and garden plans with family members</li>
                        <li>Collaborate on seasonal planning</li>
                        <li>Manage different access levels for different users</li>
                        <li>Keep track of multiple garden locations</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
