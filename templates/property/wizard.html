{% extends "base.html" %}
{% block title %}Property Wizard{% endblock %}

{% block head %}
<link rel="stylesheet" href="/static/css/wizard.css">
<script src="/static/js/draw_shapes.js" defer></script>
{% endblock %}

{% block content %}
<div class="wizard-container">
    <div class="wizard-header">
        <h1>Property Setup Wizard</h1>
        <div class="wizard-progress">
            <div class="progress-step {% if step == 1 %}active{% endif %}" data-step="1">Basic Info</div>
            <div class="progress-step {% if step == 2 %}active{% endif %}" data-step="2">Property Boundary</div>
            <div class="progress-step {% if step == 3 %}active{% endif %}" data-step="3">Growing Areas</div>
            <div class="progress-step {% if step == 4 %}active{% endif %}" data-step="4">Review</div>
        </div>
    </div>

    <div class="wizard-content">
        {% if step == 1 %}
        <form method="post" action="/property/wizard/step1" class="wizard-form">
            <div class="form-group">
                <label for="property-name">Property Name</label>
                <input type="text" id="property-name" name="name" required>
            </div>
            <div class="form-group">
                <label for="property-floors">Number of Floors</label>
                <input type="number" id="property-floors" name="floors" min="1" value="1" required>
            </div>
            <div class="form-group">
                <label for="property-type">Property Type</label>
                <select id="property-type" name="type">
                    <option value="house">House</option>
                    <option value="apartment">Apartment</option>
                    <option value="garden">Garden</option>
                    <option value="farm">Farm</option>
                </select>
            </div>
            <div class="wizard-buttons">
                <button type="submit" class="btn-primary">Next</button>
            </div>
        </form>
        {% elif step == 2 %}
        <div class="drawing-container">
            <div class="drawing-tools">
                <button id="free-draw" class="tool-btn active">Free Draw</button>
                <button id="rectangle" class="tool-btn">Rectangle</button>
                <button id="circle" class="tool-btn">Circle</button>
                <button id="toggle-grid" class="tool-btn">Toggle Grid</button>
                <button id="clear" class="tool-btn">Clear</button>
            </div>
            <div class="canvas-container">
                <canvas id="drawing-canvas" width="800" height="600"></canvas>
            </div>
            <div class="wizard-buttons">
                <a href="/property/wizard/step1" class="btn-secondary">Back</a>
                <button id="save-boundary" class="btn-primary">Next</button>
            </div>
        </div>
        {% elif step == 3 %}
        <div class="drawing-container">
            <div class="drawing-tools">
                <button id="free-draw" class="tool-btn active">Free Draw</button>
                <button id="rectangle" class="tool-btn">Rectangle</button>
                <button id="circle" class="tool-btn">Circle</button>
                <button id="toggle-grid" class="tool-btn">Toggle Grid</button>
                <button id="clear" class="tool-btn">Clear</button>
            </div>
            <div class="canvas-container">
                <canvas id="drawing-canvas" width="800" height="600"></canvas>
            </div>
            <div class="wizard-buttons">
                <a href="/property/wizard/step2" class="btn-secondary">Back</a>
                <button id="save-growing-areas" class="btn-primary">Next</button>
            </div>
        </div>
        {% elif step == 4 %}
        <div class="review-container">
            <h2>Review Your Property</h2>
            <div class="property-summary">
                <div class="property-info">
                    <h3>{{ property.name }}</h3>
                    <p>Type: {{ property.type }}</p>
                    <p>Floors: {{ property.floors }}</p>
                    <p>Total Area: {{ property.total_area }} sq ft</p>
                    <p>Growing Area: {{ property.growing_area }} sq ft</p>
                    <p>Utilization: {{ property.utilization_rate }}%</p>
                </div>
                <div class="property-preview">
                    <div id="preview-container"></div>
                </div>
            </div>
            <div class="wizard-buttons">
                <a href="/property/wizard/step3" class="btn-secondary">Back</a>
                <button id="finish-wizard" class="btn-success">Finish</button>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
    {% if step == 2 or step == 3 %}
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize drawing canvas
        initializeDrawingCanvas();

        // Set the drawing mode based on the step
        modeContext = {% if step == 2 %}"property"{% else %}"growingArea"{% endif %};

        {% if step == 3 and property_boundary %}
        // Load property boundary for reference
        const propertyBoundary = {{ property_boundary|safe }};
        loadPropertyBoundary(propertyBoundary);
        {% endif %}

        // Add save functionality
        {% if step == 2 %}
        document.getElementById('save-boundary').addEventListener('click', function() {
            const shapesData = getShapesData();
            if (shapesData.length === 0) {
                alert('Please draw at least one boundary shape');
                return;
            }

            // Send data to server
            fetch('/property/wizard/step2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ shapes: shapesData })
            }).then(response => {
                if (response.ok) {
                    window.location.href = '/property/wizard/step3';
                } else {
                    alert('Error saving boundary data');
                }
            });
        });
        {% elif step == 3 %}
        document.getElementById('save-growing-areas').addEventListener('click', function() {
            const shapesData = getShapesData();

            // Send data to server
            fetch('/property/wizard/step3', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ shapes: shapesData })
            }).then(response => {
                if (response.ok) {
                    window.location.href = '/property/wizard/step4';
                } else {
                    alert('Error saving growing area data');
                }
            });
        });
        {% endif %}
    });
    {% endif %}

    {% if step == 4 %}
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize 3D preview
        const visualizer = new PropertyVisualizer('preview-container', {
            showStats: false,
            showMinimap: false
        });

        // Load property data
        visualizer.loadPropertyData({{ property_data|safe }});

        // Finish wizard button
        document.getElementById('finish-wizard').addEventListener('click', function() {
            fetch('/property/wizard/finish', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    alert('Error: ' + data.error);
                }
            });
        });
    });
    {% endif %}
</script>
{% endblock %}