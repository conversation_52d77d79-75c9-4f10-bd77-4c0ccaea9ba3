{# src/templates/wizard/draw_growing_areas.html #}
{% extends "base.html" %}

{% block title %}Growing Areas for {{ display_floor }}{% endblock %}

{% block content %}
<div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 p-6 rounded shadow">
    <div class="wizard-progress mb-6">
        <div class="wizard-step completed">
            <div class="wizard-step-circle">1</div>
            <div class="wizard-step-label">Household</div>
        </div>
        <div class="wizard-step completed">
            <div class="wizard-step-circle">2</div>
            <div class="wizard-step-label">Invite</div>
        </div>
        <div class="wizard-step completed">
            <div class="wizard-step-circle">3</div>
            <div class="wizard-step-label">Property</div>
        </div>
        <div class="wizard-step completed">
            <div class="wizard-step-circle">4</div>
            <div class="wizard-step-label">Draw Property</div>
        </div>
        <div class="wizard-step active">
            <div class="wizard-step-circle">5</div>
            <div class="wizard-step-label">Growing Areas</div>
        </div>
    </div>

    <h2 class="text-2xl font-bold mb-4">Growing Areas: {{ display_floor }}</h2>

    {# Load existing property shapes for boundaries #}
    <textarea id="propertyBoundaryData" class="hidden">{{ property_boundary_shapes | safe }}</textarea>
    {# Load existing growing area shapes #}
    <textarea id="existingGAData" class="hidden">{{ existing_growing_shapes | safe }}</textarea>

    <div class="mb-4">
        <div class="drawing-tools">
            <button onclick="setShapeType('FreeDraw')" class="tool-button">
                <span class="icon">✏️</span>
            </button>
            <button onclick="setShapeType('Polygon')" class="tool-button">
                <span class="icon">🔷</span>
            </button>
            <button onclick="setShapeType('Circle')" class="tool-button">
                <span class="icon">⭕</span>
            </button>
            <button onclick="setShapeType('Square')" class="tool-button">
                <span class="icon">⬛</span>
            </button>
            <button onclick="eraseLastShape()" class="tool-button">
                <span class="icon">🗑️</span>
            </button>
            <button onclick="toggleGrid()" class="tool-button">
                <span class="icon">📏</span>
            </button>
        </div>

        <div class="relative w-full h-96 bg-white dark:bg-gray-900 rounded shadow canvas-container">
            <canvas id="drawCanvas" class="w-full h-full border border-gray-300 rounded"></canvas>
            <div class="absolute top-2 right-2 bg-white dark:bg-gray-800 p-2 rounded shadow text-sm">
                <div>Floor: <span class="font-bold">{{ display_floor }}</span></div>
                <div>Available Area: <span class="font-bold" id="availableAreaDisplay">Ready to draw</span></div>
            </div>
        </div>

        <div class="flex gap-2 mt-2">
            <button onclick="eraseLastShape()" class="btn-danger">Undo Last</button>
            <button onclick="startOverCanvas()" class="btn-danger">Start Over</button>
            <button onclick="toggleGrid()" class="btn-secondary">Toggle Grid</button>
        </div>
    </div>

    <form id="shapeForm" action="/wizard/save_growing_area_shape" method="post">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <input type="hidden" name="shape_data" id="shapeDataHidden">
        <input type="hidden" name="shape_type" id="shapeTypeHidden">
        <input type="hidden" name="floor_no" value="{{ current_floor }}">
        <input type="hidden" name="area" id="areaHidden">
        <button type="button" onclick="trySaveShape()" class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">
            Save Growing Shape
        </button>
    </form>

    <div class="mt-4 text-center">
        <a href="/wizard/finish_grow_floor" class="inline-block bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
            Finish {{ display_floor }}
        </a>
    </div>
</div>

<!-- Dimension Popup with improved contrast -->
<div id="dimensionPopup" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div id="dimensionPopupInner" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl">
        <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Enter Area (m²)</h3>
        <div class="mb-4">
            <input type="number"
                   id="dimensionInput"
                   class="w-full px-3 py-2 border rounded text-gray-900 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                   placeholder="Enter area..." step="0.01" min="0.01">
        </div>
        <div class="flex justify-end gap-3">
            <button onclick="closeDimensionPopup()"
                    class="btn-secondary">
                Cancel
            </button>
            <button onclick="saveDimensionAndSubmit()"
                    class="btn-success">
                Save
            </button>
        </div>
    </div>
</div>

<script src="/static/js/draw_shapes.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const rawGA = document.getElementById("existingGAData").value || "[]";
        let existingGA = [];
        try {
            existingGA = JSON.parse(rawGA);
        } catch (e) {
            console.error("Failed to parse existing growing areas:", e);
        }

        // Load property boundary data if available
        const propertyData = document.getElementById("propertyBoundaryData");
        if (propertyData) {
            try {
                const propertyBoundary = JSON.parse(propertyData.value || "[]");
                if (propertyBoundary.length > 0) {
                    loadPropertyBoundary(propertyBoundary);
                }
            } catch(e) {
                console.error("Failed to parse property boundary:", e);
            }
        }

        initDrawing("growingArea", existingGA, true);

        // Update area display
        const display = document.getElementById('availableAreaDisplay');
        if (display) {
            display.textContent = 'Draw growing areas within property bounds';
        }

        // Update area display when shapes change
        function updateAreaDisplay() {
            const display = document.getElementById('availableAreaDisplay');
            if (display && shapes.length > 0) {
                let totalArea = 0;
                shapes.forEach(shape => {
                    totalArea += calculateArea(shape.points);
                });
                const pixelsPerMeter = 20;
                const areaInSquareMeters = totalArea / (pixelsPerMeter * pixelsPerMeter);
                display.textContent = `Current area: ${areaInSquareMeters.toFixed(2)} m²`;
            } else if (display) {
                display.textContent = 'Draw growing areas within property bounds';
            }
        }

        // Override the redrawCanvas function to update area display
        const originalRedrawCanvas = window.redrawCanvas;
        window.redrawCanvas = function() {
            if (originalRedrawCanvas) {
                originalRedrawCanvas();
            }
            updateAreaDisplay();
        };
    });
</script>
{% endblock %}
