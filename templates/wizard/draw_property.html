{% extends "base.html" %}
{% block title %}Draw Property{% endblock %}
{% block content %}

<style>
.drawing-tools {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.tool-button {
    padding: 8px 12px;
    border: 2px solid #d1d5db;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    min-height: 48px;
}

.tool-button:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.tool-button.active {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
}

.canvas-container {
    border: 2px solid #d1d5db;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    height: 600px;
    position: relative;
}

#drawCanvas {
    display: block;
    cursor: crosshair;
}
</style>
<div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 p-6 rounded shadow">
    <div class="wizard-progress mb-6">
        <div class="wizard-step completed">
            <div class="wizard-step-circle">1</div>
            <div class="wizard-step-label">Household</div>
        </div>
        <div class="wizard-step completed">
            <div class="wizard-step-circle">2</div>
            <div class="wizard-step-label">Invite</div>
        </div>
        <div class="wizard-step completed">
            <div class="wizard-step-circle">3</div>
            <div class="wizard-step-label">Property</div>
        </div>
        <div class="wizard-step active">
            <div class="wizard-step-circle">4</div>
            <div class="wizard-step-label">Draw Property</div>
        </div>
        <div class="wizard-step">
            <div class="wizard-step-circle">5</div>
            <div class="wizard-step-label">Growing Areas</div>
        </div>
    </div>

    <h2 class="text-2xl font-bold mb-4">Draw Shapes for {{ display_floor }}</h2>

    <div class="mb-4">
        <div class="drawing-tools">
            <button onclick="setShapeType('FreeDraw')" class="tool-button">
                <span class="icon">✏️</span>
            </button>
            <button onclick="setShapeType('Polygon')" class="tool-button">
                <span class="icon">🔷</span>
            </button>
            <button onclick="setShapeType('Circle')" class="tool-button">
                <span class="icon">⭕</span>
            </button>
            <button onclick="setShapeType('Square')" class="tool-button">
                <span class="icon">⬛</span>
            </button>
            <button onclick="eraseLastShape()" class="tool-button">
                <span class="icon">🗑️</span>
            </button>
            <button onclick="toggleGrid()" class="tool-button">
                <span class="icon">📏</span>
            </button>
        </div>

        <div class="canvas-container">
            <canvas id="drawCanvas" class="w-full h-full border border-gray-300 rounded"></canvas>
        </div>

        <div class="flex gap-2 mt-2">
            <button onclick="eraseLastShape()" class="btn-danger">Undo Last</button>
            <button onclick="startOverCanvas()" class="btn-danger">Start Over</button>
            <button onclick="toggleGrid()" class="btn-secondary">Toggle Grid</button>
        </div>
    </div>

    <textarea id="existingShapesData" class="hidden">{{ existing_shapes }}</textarea>
    <input type="hidden" id="outsideAreaValue" value="{{ outside_area|default(value=0) }}">
    <input type="hidden" id="insideAreaValue" value="{{ inside_area|default(value=0) }}">

    <form id="shapeForm" action="/wizard/save_property_shape" method="post">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <input type="hidden" name="shape_data" id="shapeDataHidden">
        <input type="hidden" name="shape_type" id="shapeTypeHidden">
        <input type="hidden" name="area" id="areaHidden">
        <input type="hidden" name="floor_no" value="{{ current_floor }}">
        <button type="button" onclick="trySaveShape()" class="btn-success w-full">
            Save Current Shape
        </button>
    </form>

    <div class="mt-4 text-center">
        <form action="/wizard/finish_floor" method="post">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            <input type="hidden" name="floor_no" value="{{ current_floor }}">
            <button type="submit" class="btn-primary">
                Finish {{ display_floor }}
            </button>
        </form>
    </div>
</div>

<!-- Dimension Popup -->
<div id="dimensionPopup" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div id="dimensionPopupInner" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl">
        <h3 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Enter Area (m²)</h3>
        <div class="mb-4">
            <input type="text"
                   id="dimensionInput"
                   class="w-full px-3 py-2 border rounded text-gray-900 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                   placeholder="Enter area...">
        </div>
        <div class="flex justify-end gap-3">
            <button onclick="closeDimensionPopup()"
                    class="btn-secondary">
                Cancel
            </button>
            <button onclick="saveDimensionAndSubmit()"
                    class="btn-success">
                Save
            </button>
        </div>
    </div>
</div>

<script src="/static/js/draw_shapes.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const rawData = document.getElementById("existingShapesData").value || "[]";
        let existingShapes = [];
        try {
            existingShapes = JSON.parse(rawData);
        } catch(e) {
            console.error("Failed to parse existing shapes:", e);
        }
        initDrawing("property", existingShapes, true);
    });
</script>
{% endblock %}
