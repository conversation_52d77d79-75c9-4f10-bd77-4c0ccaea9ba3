{# templates/wizard/invite_members.html #}
{% extends "base.html" %}
{% block title %}Invite Members{% endblock %}
{% block content %}
<div class="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded shadow">
  <h1 class="text-2xl font-bold mb-4">Invite Household Members</h1>
  <form method="post" action="/wizard/invite">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    <div class="mb-6">
      <label for="emails" class="block text-sm font-medium mb-2">Emails (comma-separated):</label>
      <textarea
        id="emails"
        name="emails"
        rows="3"
        class="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-transparent"
      ></textarea>
    </div>
    <div class="flex gap-4">
      <button
        type="submit"
        class="flex-1 bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition duration-150"
      >
        Next
      </button>
      <a
        href="/wizard/property"
        class="flex-1 inline-block bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition duration-150 text-center"
      >
        Skip
      </a>
    </div>
  </form>
</div>
{% endblock %}
