{# templates/wizard/create_property.html #}
{% extends "base.html" %}
{% block title %}Create Property{% endblock %}
{% block content %}
<div class="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded shadow">
    <h1 class="text-2xl font-bold mb-4">Property Details</h1>
    <form method="post" action="/wizard/property">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <div class="mb-4">
            <label for="name" class="block text-sm font-medium">Property Name:</label>
            <input type="text" id="name" name="name" required
                   class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
        </div>
        <div class="mb-4">
            <label for="outside_area" class="block text-sm font-medium">Outside Area (m²):</label>
            <input type="number" id="outside_area" name="outside_area" min="0" placeholder="0"
                   class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
        </div>
        <div class="mb-4">
            <label for="inside_area" class="block text-sm font-medium">Inside Area (m²):</label>
            <input type="number" id="inside_area" name="inside_area" min="0" placeholder="0"
                   class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
        </div>
        <div class="mb-4">
            <label for="floors" class="block text-sm font-medium">Number of Floors:</label>
            <input type="number" id="floors" name="floors" min="0" placeholder="0"
                   class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
        </div>
        <button type="submit"
                class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">
            Next
        </button>
    </form>
</div>
{% endblock %}
