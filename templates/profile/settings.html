{% extends "base.html" %}
{% block title %}Account Settings{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <h1 class="text-3xl font-bold">Account Settings</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">Manage your account security and preferences.</p>
    </div>

    {% if error %}
    <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm text-red-800 dark:text-red-200">{{ error }}</p>
            </div>
        </div>
    </div>
    {% endif %}

    {% if success %}
    <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm text-green-800 dark:text-green-200">{{ success }}</p>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Change Password -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Change Password</h2>
            
            <form action="/profile/change-password" method="post">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="mb-4">
                    <label for="current_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Current Password
                    </label>
                    <input type="password" id="current_password" name="current_password" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div class="mb-4">
                    <label for="new_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        New Password
                    </label>
                    <input type="password" id="new_password" name="new_password" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div class="mb-6">
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Confirm New Password
                    </label>
                    <input type="password" id="confirm_password" name="confirm_password" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white">
                </div>

                <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                    Update Password
                </button>
            </form>
        </div>

        <!-- Account Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Account Information</h2>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
                    <p class="text-gray-900 dark:text-gray-100">{{ user.username }}</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Role</label>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                               {% if user.role == 'superadmin' %}bg-purple-100 text-purple-800
                               {% elif user.role == 'admin' %}bg-blue-100 text-blue-800
                               {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ user.role | title }}
                    </span>
                </div>
            </div>

            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-medium mb-3">Quick Links</h3>
                <div class="space-y-2">
                    <a href="/profile" class="block text-green-600 hover:text-green-800">
                        ← Back to Profile
                    </a>
                    <a href="/households" class="block text-green-600 hover:text-green-800">
                        My Households
                    </a>
                    <a href="/property" class="block text-green-600 hover:text-green-800">
                        My Properties
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="mt-8 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-6">
        <h3 class="text-lg font-medium text-red-800 dark:text-red-200 mb-2">Danger Zone</h3>
        <p class="text-sm text-red-700 dark:text-red-300 mb-4">
            These actions are irreversible. Please be certain before proceeding.
        </p>
        <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm" disabled>
            Delete Account (Coming Soon)
        </button>
    </div>
</div>
{% endblock %}
