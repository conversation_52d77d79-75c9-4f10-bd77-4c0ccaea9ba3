#!/usr/bin/env python3
"""
Comprehensive test for Garden Planner Application
Tests all major functionality including registration, household creation, property management, etc.
"""

import requests
import re
import random
import time
import sys

def get_csrf_token(html_content):
    """Extract CSRF token from HTML"""
    match = re.search(r'name="csrf_token" value="([^"]+)"', html_content)
    return match.group(1) if match else None

def test_server_health():
    """Test if server is responding"""
    try:
        response = requests.get("http://127.0.0.1:8080", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_theme_toggle():
    """Test that theme toggle is visible"""
    print("Testing theme toggle visibility...")
    
    try:
        response = requests.get("http://127.0.0.1:8080", timeout=10)
        if response.status_code != 200:
            print(f"❌ Homepage failed to load: {response.status_code}")
            return False
            
        content = response.text
        if 'id="theme-toggle"' in content and 'theme-toggle-dark-icon' in content:
            print("✓ Theme toggle visible")
            return True
        else:
            print("❌ Theme toggle not found")
            return False
    except Exception as e:
        print(f"❌ Theme toggle test failed: {e}")
        return False

def test_registration_flow():
    """Test the complete registration and setup flow"""
    print("Testing registration and setup flow...")
    
    session = requests.Session()
    
    try:
        # Test registration page
        response = session.get("http://127.0.0.1:8080/auth/register", timeout=10)
        if response.status_code != 200:
            print(f"❌ Registration page failed: {response.status_code}")
            return False
            
        csrf_token = get_csrf_token(response.text)
        if not csrf_token:
            print("❌ No CSRF token found on registration page")
            return False
            
        print("✓ Registration page loads with CSRF token")
        
        # Submit registration
        username = f"testuser_{random.randint(1000, 9999)}"
        password = "testpass123"
        
        data = {
            'username': username,
            'password': password,
            'password_confirm': password,
            'csrf_token': csrf_token
        }
        
        response = session.post("http://127.0.0.1:8080/auth/register", data=data, timeout=10)
        
        if response.status_code == 200 and "household" in response.text.lower():
            print("✓ Registration successful (on household page)")
        elif response.status_code == 302:
            print("✓ Registration successful (redirected)")
        else:
            print(f"❌ Registration failed: {response.status_code}")
            return False
        
        # Test household creation
        response = session.get("http://127.0.0.1:8080/wizard/household", timeout=10)
        if response.status_code != 200:
            print(f"❌ Household page failed: {response.status_code}")
            return False
            
        csrf_token = get_csrf_token(response.text)
        if not csrf_token:
            print("❌ No CSRF token found on household page")
            return False
            
        print("✓ Household creation page loads with CSRF token")
        
        # Submit household creation
        household_name = f"Test Household {random.randint(100, 999)}"
        data = {
            'name': household_name,
            'csrf_token': csrf_token
        }
        
        response = session.post("http://127.0.0.1:8080/wizard/household", data=data, timeout=10)
        
        if response.status_code in [200, 302]:
            print("✓ Household creation successful")
            return True
        else:
            print(f"❌ Household creation failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Registration flow test failed: {e}")
        return False

def test_navigation():
    """Test that navigation works for authenticated users"""
    print("Testing navigation...")
    
    session = requests.Session()
    
    try:
        # Register and login first
        response = session.get("http://127.0.0.1:8080/auth/register", timeout=10)
        csrf_token = get_csrf_token(response.text)
        
        username = f"navtest_{random.randint(1000, 9999)}"
        data = {
            'username': username,
            'password': "testpass123",
            'password_confirm': "testpass123",
            'csrf_token': csrf_token
        }
        
        session.post("http://127.0.0.1:8080/auth/register", data=data, timeout=10)
        
        # Test main navigation links
        nav_links = [
            "/plants/list",
            "/seeds/list", 
            "/property",
            "/season_plans",
            "/notifications/list"
        ]
        
        for link in nav_links:
            response = session.get(f"http://127.0.0.1:8080{link}", timeout=10)
            if response.status_code == 200:
                print(f"✓ {link} accessible")
            else:
                print(f"❌ {link} failed: {response.status_code}")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Navigation test failed: {e}")
        return False

def main():
    print("🌱 Garden Planner Comprehensive Test Suite")
    print("=" * 50)
    
    # Test server health first
    print("Checking server health...")
    if not test_server_health():
        print("❌ Server is not responding. Make sure it's running on port 8080.")
        sys.exit(1)
    print("✓ Server is responding")
    
    # Run all tests
    tests = [
        test_theme_toggle,
        test_registration_flow,
        test_navigation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            time.sleep(1)  # Brief pause between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Garden Planner is working correctly.")
        print("\nFeatures verified:")
        print("✓ Theme toggle visible for all users")
        print("✓ User registration and authentication")
        print("✓ Household creation wizard")
        print("✓ Navigation and page accessibility")
        print("✓ CSRF protection working")
        return True
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
