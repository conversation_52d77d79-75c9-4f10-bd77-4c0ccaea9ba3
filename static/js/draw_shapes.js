
// Drawing canvas functionality for property and growing area shapes
let canvas, ctx;
let isDrawing = false;
let currentTool = 'free-draw';
let currentShape = [];
let shapes = [];
let savedShapes = []; // Shapes that have been saved to the database
let showGrid = true;
let gridSize = 20;
let modeContext = 'property'; // 'property' or 'growingArea'
let propertyBoundary = null;

function initializeDrawingCanvas() {
    // Try multiple canvas IDs for compatibility
    canvas = document.getElementById('drawing-canvas') || document.getElementById('drawCanvas');
    if (!canvas) {
        console.error('Canvas element not found');
        return;
    }

    ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('Could not get canvas context');
        return;
    }

    // Set canvas size explicitly and ensure it's visible
    canvas.width = 800;
    canvas.height = 600;
    canvas.style.border = '2px solid #ccc';
    canvas.style.backgroundColor = '#ffffff';
    canvas.style.display = 'block';
    canvas.style.cursor = 'crosshair';

    console.log('Canvas initialized:', canvas.width, 'x', canvas.height);

    // Set up event listeners
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Tool selection
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentTool = this.id;
        });
    });

    // Clear button
    document.getElementById('clear').addEventListener('click', clearCanvas);

    // Toggle grid
    document.getElementById('toggle-grid').addEventListener('click', toggleGrid);

    // Initial draw
    clearCanvas();
}

function startDrawing(e) {
    isDrawing = true;
    const rect = canvas.getBoundingClientRect();

    // For freehand drawing, use exact cursor position, for other tools use grid snapping
    let x, y;
    if (currentTool === 'free-draw') {
        x = e.clientX - rect.left;
        y = e.clientY - rect.top;
    } else {
        x = Math.round((e.clientX - rect.left) / gridSize) * gridSize;
        y = Math.round((e.clientY - rect.top) / gridSize) * gridSize;
    }

    currentShape = [];

    if (currentTool === 'free-draw') {
        currentShape.push({ x, y });
    } else if (currentTool === 'rectangle') {
        currentShape.push({ x, y }); // Starting point
    } else if (currentTool === 'circle') {
        currentShape.push({ x, y }); // Center point
    }

    redrawCanvas();
}

function draw(e) {
    if (!isDrawing) return;

    const rect = canvas.getBoundingClientRect();

    // For freehand drawing, use exact cursor position, for other tools use grid snapping
    let x, y;
    if (currentTool === 'free-draw') {
        x = e.clientX - rect.left;
        y = e.clientY - rect.top;
    } else {
        x = Math.round((e.clientX - rect.left) / gridSize) * gridSize;
        y = Math.round((e.clientY - rect.top) / gridSize) * gridSize;
    }

    if (currentTool === 'free-draw') {
        // Add point to current shape
        currentShape.push({ x, y });
    } else if (currentTool === 'rectangle' || currentTool === 'circle') {
        // Update the second point (for preview)
        if (currentShape.length > 1) {
            currentShape[1] = { x, y };
        } else {
            currentShape.push({ x, y });
        }
    }

    redrawCanvas();
}

function stopDrawing() {
    if (!isDrawing) return;
    isDrawing = false;

    if (currentShape.length < 2) return;

    if (currentTool === 'rectangle') {
        // Convert rectangle to polygon points
        const startX = currentShape[0].x;
        const startY = currentShape[0].y;
        const endX = currentShape[1].x;
        const endY = currentShape[1].y;

        currentShape = [
            { x: startX, y: startY },
            { x: endX, y: startY },
            { x: endX, y: endY },
            { x: startX, y: endY },
            { x: startX, y: startY } // Close the shape
        ];
    } else if (currentTool === 'circle') {
        // Convert circle to polygon points
        const centerX = currentShape[0].x;
        const centerY = currentShape[0].y;
        const radius = Math.sqrt(
            Math.pow(currentShape[1].x - centerX, 2) +
            Math.pow(currentShape[1].y - centerY, 2)
        );

        const segments = 36; // Number of segments to approximate circle
        currentShape = [];

        for (let i = 0; i <= segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            currentShape.push({ x: Math.round(x / gridSize) * gridSize, y: Math.round(y / gridSize) * gridSize });
        }
    }

    // Add the shape to our collection
    shapes.push({
        type: modeContext === 'property' ? 'boundary' : 'growingArea',
        points: currentShape
    });

    currentShape = [];
    redrawCanvas();
}

function clearCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (showGrid) {
        drawGrid();
    }

    // If we're in growing area mode, draw the property boundary first
    if (modeContext === 'growingArea' && propertyBoundary) {
        drawPropertyBoundary();
    }

    // Draw saved shapes (dimmed)
    savedShapes.forEach(shape => {
        drawShape(shape.points, shape.type === 'boundary' ? '#3b82f680' : '#10b98180', true);
    });

    // Draw current unsaved shapes
    shapes.forEach(shape => {
        drawShape(shape.points, shape.type === 'boundary' ? '#3b82f6' : '#10b981');
    });

    // Draw current shape
    if (currentShape.length > 0) {
        if (currentTool === 'rectangle' && currentShape.length === 2) {
            // Preview rectangle
            const startX = currentShape[0].x;
            const startY = currentShape[0].y;
            const endX = currentShape[1].x;
            const endY = currentShape[1].y;

            ctx.beginPath();
            ctx.rect(startX, startY, endX - startX, endY - startY);
            ctx.strokeStyle = modeContext === 'property' ? '#3b82f6' : '#10b981';
            ctx.lineWidth = 3;
            ctx.stroke();

            // Fill with semi-transparent color
            ctx.fillStyle = (modeContext === 'property' ? '#3b82f6' : '#10b981') + '40';
            ctx.fill();
        } else if (currentTool === 'circle' && currentShape.length === 2) {
            // Preview circle
            const centerX = currentShape[0].x;
            const centerY = currentShape[0].y;
            const radius = Math.sqrt(
                Math.pow(currentShape[1].x - centerX, 2) +
                Math.pow(currentShape[1].y - centerY, 2)
            );

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.strokeStyle = modeContext === 'property' ? '#3b82f6' : '#10b981';
            ctx.lineWidth = 3;
            ctx.stroke();

            // Fill with semi-transparent color
            ctx.fillStyle = (modeContext === 'property' ? '#3b82f6' : '#10b981') + '40';
            ctx.fill();
        } else {
            // Draw free-form shape
            drawShape(currentShape, modeContext === 'property' ? '#3b82f6' : '#10b981');
        }
    }
}

function drawShape(points, color, isDimmed = false) {
    if (points.length < 2) return;

    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);

    for (let i = 1; i < points.length; i++) {
        ctx.lineTo(points[i].x, points[i].y);
    }

    ctx.strokeStyle = color;
    ctx.lineWidth = isDimmed ? 1 : 3;
    ctx.globalAlpha = isDimmed ? 0.5 : 1.0;
    ctx.stroke();

    // Fill with semi-transparent color
    ctx.fillStyle = color + (isDimmed ? '20' : '40'); // Different opacity for dimmed
    ctx.fill();

    // Reset alpha
    ctx.globalAlpha = 1.0;
}

function drawGrid() {
    ctx.beginPath();
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 0.5;

    // Draw vertical lines
    for (let x = 0; x <= canvas.width; x += gridSize) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
    }

    // Draw horizontal lines
    for (let y = 0; y <= canvas.height; y += gridSize) {
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
    }

    ctx.stroke();
}

function toggleGrid() {
    showGrid = !showGrid;
    console.log('Grid toggled:', showGrid ? 'ON' : 'OFF');

    // Update button appearance
    const gridButtons = document.querySelectorAll('button[onclick="toggleGrid()"]');
    gridButtons.forEach(btn => {
        if (showGrid) {
            btn.classList.add('active');
            btn.style.backgroundColor = '#3b82f6';
            btn.style.color = 'white';
        } else {
            btn.classList.remove('active');
            btn.style.backgroundColor = '';
            btn.style.color = '';
        }
    });

    redrawCanvas();
}

function redrawCanvas() {
    console.log('Redrawing canvas with', shapes.length, 'shapes');
    clearCanvas();
}

function loadPropertyBoundary(boundary) {
    propertyBoundary = boundary;
    redrawCanvas();
}

function drawPropertyBoundary() {
    propertyBoundary.forEach(shape => {
        drawShape(shape.points, '#3b82f6');
    });
}

function getShapesData() {
    return shapes;
}

function calculateArea(points) {
    let area = 0;
    for (let i = 0; i < points.length - 1; i++) {
        area += points[i].x * points[i + 1].y - points[i + 1].x * points[i].y;
    }
    return Math.abs(area / 2);
}

// Global initialization function for templates
function initDrawing(mode, existingShapes, showGridDefault) {
    console.log('Initializing drawing with mode:', mode, 'shapes:', existingShapes);
    modeContext = mode || 'property';
    showGrid = showGridDefault !== undefined ? showGridDefault : true;

    if (existingShapes && Array.isArray(existingShapes)) {
        shapes = existingShapes;
        console.log('Loaded', shapes.length, 'existing shapes');
    }

    initializeDrawingCanvas();

    // Initial redraw to show grid and any existing shapes
    if (canvas && ctx) {
        redrawCanvas();
    }
}

// Tool functions for template buttons
function setShapeType(type) {
    console.log('Setting shape type to:', type);
    switch(type) {
        case 'FreeDraw':
            currentTool = 'free-draw';
            break;
        case 'Polygon':
            currentTool = 'free-draw'; // Use free-draw for polygon
            break;
        case 'Circle':
            currentTool = 'circle';
            break;
        case 'Square':
            currentTool = 'rectangle';
            break;
        default:
            currentTool = 'free-draw';
    }

    // Update button states
    document.querySelectorAll('.tool-button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.closest('.tool-button').classList.add('active');
}

function eraseLastShape() {
    if (shapes.length > 0) {
        shapes.pop();
        console.log('Removed last shape, remaining:', shapes.length);
        redrawCanvas();
    }
}

function startOverCanvas() {
    shapes = [];
    currentShape = [];
    console.log('Cleared all shapes');
    redrawCanvas();
}

function trySaveShape() {
    if (shapes.length === 0) {
        alert('Please draw at least one shape before saving.');
        return;
    }

    // Calculate total area
    let totalArea = 0;
    shapes.forEach(shape => {
        totalArea += calculateArea(shape.points);
    });

    // Convert area from pixels to square meters (assuming some scale)
    const pixelsPerMeter = 20; // Adjust this based on your scale
    const areaInSquareMeters = totalArea / (pixelsPerMeter * pixelsPerMeter);

    // Fill form data
    document.getElementById('shapeDataHidden').value = JSON.stringify(shapes);
    document.getElementById('shapeTypeHidden').value = 'boundary';
    document.getElementById('areaHidden').value = areaInSquareMeters.toFixed(2);

    // Show dimension popup for user to confirm/adjust area
    showDimensionPopup(areaInSquareMeters.toFixed(2));
}

function showDimensionPopup(calculatedArea) {
    document.getElementById('dimensionInput').value = calculatedArea;
    document.getElementById('dimensionPopup').classList.remove('hidden');
}

function closeDimensionPopup() {
    document.getElementById('dimensionPopup').classList.add('hidden');
}

function saveDimensionAndSubmit() {
    const area = document.getElementById('dimensionInput').value;
    if (!area || isNaN(area) || parseFloat(area) <= 0) {
        alert('Please enter a valid area value.');
        return;
    }

    document.getElementById('areaHidden').value = area;
    closeDimensionPopup();

    // Move current shapes to saved shapes before submitting
    savedShapes.push(...shapes);
    shapes = [];
    redrawCanvas();

    // Submit the form
    document.getElementById('shapeForm').submit();
}

// Function to mark shapes as saved (called after successful save)
function markShapesAsSaved() {
    savedShapes.push(...shapes);
    shapes = [];
    redrawCanvas();
}

// Auto-initialize if canvas is found on page load
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('drawing-canvas') || document.getElementById('drawCanvas')) {
        initializeDrawingCanvas();
    }
});
